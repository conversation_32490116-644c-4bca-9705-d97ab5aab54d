'use client';

import { useState } from 'react';
import AdminLayout from '@/layouts/signinLayout/AdminLayout';
import LandingPage from '@/screen/landing';

/**
 * Test page for responsive behavior
 * This page helps test the height calculation issues when dragging between screen sizes
 */

export default function TestResponsivePage() {
  const [isLoading, setIsLoading] = useState(false);

  const triggerLoading = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 3000);
  };

  return (
    <AdminLayout isLoading={isLoading}>
      <div className="p-5">
        <div className="bg-white rounded-xl p-6 mb-6">
          <h1 className="text-2xl font-bold mb-4">Responsive Height Test</h1>
          <p className="text-gray-600 mb-4">
            This page tests the height calculation when resizing the browser window.
            Try the following:
          </p>
          
          <ol className="list-decimal list-inside text-sm text-gray-600 space-y-2 mb-6">
            <li>Start with a large screen (desktop view)</li>
            <li>Drag the browser window to make it smaller (mobile view)</li>
            <li>Drag it back to large size</li>
            <li>Check if the recommendation cards adjust properly</li>
            <li>Use the button below to test with skeleton loading</li>
          </ol>
          
          <button
            onClick={triggerLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Test Skeleton Loading (3s)
          </button>
          
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold mb-2">Current Window Size:</h3>
            <p className="text-sm text-gray-600">
              Width: {typeof window !== 'undefined' ? window.innerWidth : 'N/A'}px, 
              Height: {typeof window !== 'undefined' ? window.innerHeight : 'N/A'}px
            </p>
          </div>
        </div>

        {/* Content Area */}
        <LandingPage isLoading={isLoading} />
      </div>
    </AdminLayout>
  );
}
