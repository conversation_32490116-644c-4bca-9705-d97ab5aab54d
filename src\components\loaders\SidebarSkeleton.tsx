/**
 * SidebarSkeleton Component
 * 
 * Skeleton loader for the Sidebar component that matches the original layout:
 * - Logo section at top
 * - Menu sections with navigation items
 * - Profile section at bottom (when logged in)
 * - Responsive behavior for mobile/desktop
 */

const SidebarSkeleton = () => {
  return (
    <div className="sidebar-wrapper relative w-full">
      {/* Logo Section */}
      <div className="flex justify-between items-center p-4">
        <div className="flex items-center gap-2">
          {/* Logo Skeleton */}
          <div className="w-10 h-10 bg-gray-200 rounded-md animate-pulse" />
          {/* Logo Text Skeleton */}
          <div className="w-16 h-5 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Menu Sections */}
      <div className="flex flex-col flex-1 gap-3 p-3">
        {/* First Menu Section */}
        <div>
          {/* Section Title Skeleton */}
          <div className="w-20 h-3 bg-gray-200 rounded animate-pulse mb-2" />
          
          {/* Menu Items Skeleton */}
          {[1, 2, 3].map((item) => (
            <div key={item} className="flex items-center gap-3 p-3 mb-1">
              {/* Icon Skeleton */}
              <div className="w-5 h-5 bg-gray-200 rounded animate-pulse" />
              {/* Label Skeleton */}
              <div className="w-24 h-4 bg-gray-200 rounded animate-pulse" />
            </div>
          ))}
        </div>

        {/* Second Menu Section */}
        <div>
          {/* Section Title Skeleton */}
          <div className="w-16 h-3 bg-gray-200 rounded animate-pulse mb-2" />
          
          {/* Menu Items Skeleton */}
          {[1, 2].map((item) => (
            <div key={item} className="flex items-center gap-3 p-3 mb-1">
              {/* Icon Skeleton */}
              <div className="w-5 h-5 bg-gray-200 rounded animate-pulse" />
              {/* Label Skeleton */}
              <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
            </div>
          ))}
        </div>
      </div>

      {/* Profile Section */}
      <div className="flex justify-between items-center p-4">
        <div className="flex items-center gap-2">
          {/* Profile Image Skeleton */}
          <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
          {/* Profile Info Skeleton */}
          <div className="flex flex-col gap-1">
            <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
            <div className="w-24 h-3 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
        {/* Chevron Skeleton */}
        <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  );
};

export default SidebarSkeleton;
