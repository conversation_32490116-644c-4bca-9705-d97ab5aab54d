/**
 * ChatSkeleton Component
 *
 * Skeleton loader for the chat section that matches the original layout:
 * - Responsive chat area with proper height management
 * - Matches the dark background and rounded corners
 * - Adapts to different screen sizes
 */

const ChatSkeleton = () => {
  return (
    <div className="w-full h-full bg-[#17074C] rounded-xl p-4 sm:p-6 flex flex-col min-h-[400px]">
      {/* Chat Header Skeleton */}
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <div className="w-24 sm:w-32 h-5 sm:h-6 bg-white/20 rounded animate-pulse" />
        <div className="w-6 h-6 sm:w-8 sm:h-8 bg-white/20 rounded-full animate-pulse" />
      </div>

      {/* Chat Messages Area Skeleton */}
      <div className="flex-1 space-y-3 sm:space-y-4 mb-4 overflow-hidden">
        {/* Message bubbles skeleton */}
        {[1, 2].map((item) => (
          <div key={item} className="flex flex-col space-y-2">
            {/* User message */}
            <div className="flex justify-end">
              <div className="w-32 sm:w-48 h-10 sm:h-12 bg-white/20 rounded-lg animate-pulse max-w-[80%]" />
            </div>
            {/* Bot response */}
            <div className="flex justify-start">
              <div className="w-40 sm:w-64 h-12 sm:h-16 bg-white/10 rounded-lg animate-pulse max-w-[85%]" />
            </div>
          </div>
        ))}

        {/* Additional message for larger screens */}
        <div className="hidden sm:flex flex-col space-y-2">
          <div className="flex justify-end">
            <div className="w-36 h-10 bg-white/20 rounded-lg animate-pulse max-w-[80%]" />
          </div>
          <div className="flex justify-start">
            <div className="w-52 h-14 bg-white/10 rounded-lg animate-pulse max-w-[85%]" />
          </div>
        </div>
      </div>

      {/* Chat Input Area Skeleton */}
      <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
        <div className="flex-1 h-10 sm:h-12 bg-white/20 rounded-full animate-pulse" />
        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-full animate-pulse flex-shrink-0" />
      </div>
    </div>
  );
};

export default ChatSkeleton;
