import HeaderSkeleton from './HeaderSkeleton';
import SidebarSkeleton from './SidebarSkeleton';

/**
 * AdminLayoutSkeleton Component
 * 
 * Skeleton loader for the AdminLayout component that matches the original layout:
 * - Sidebar on the left
 * - Header at the top
 * - Main content area with proper spacing and background
 * - Responsive design for mobile/desktop
 */

interface AdminLayoutSkeletonProps {
  children?: React.ReactNode;
}

const AdminLayoutSkeleton = ({ children }: AdminLayoutSkeletonProps) => {
  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar Skeleton */}
      <div className="w-64 bg-white border-r border-gray-200">
        <SidebarSkeleton />
      </div>
      
      {/* Main Content Area */}
      <div className="flex flex-col flex-1">
        {/* Header Skeleton */}
        <div className="sticky top-0 z-10 bg-white border-b border-gray-200">
          <HeaderSkeleton />
        </div>
        
        {/* Main Content */}
        <main className="flex-1 max-h-[calc(100vh-73px)] min-h-[calc(100vh-73px)] overflow-auto ml-6 pt-0 bg-[#F5F5F5] rounded-tl-2xl">
          {children || (
            <div className="p-5 h-full">
              {/* Content skeleton placeholder */}
              <div className="space-y-4">
                <div className="w-48 h-8 bg-gray-200 rounded animate-pulse" />
                <div className="w-96 h-6 bg-gray-200 rounded animate-pulse" />
                <div className="w-full h-64 bg-gray-200 rounded-xl animate-pulse" />
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default AdminLayoutSkeleton;
