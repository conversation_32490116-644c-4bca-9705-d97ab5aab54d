import RecommendationCardSkeleton from './RecommendationCardSkeleton';

/**
 * RecommendationSkeleton Component
 *
 * Skeleton loader for the Recommendation section that matches the original layout:
 * - Header with title and filter button
 * - Recommendation cards container
 * - Responsive design and proper height management
 */

interface RecommendationSkeletonProps {
  cardCount?: number; // Number of skeleton cards to show
}

const RecommendationSkeleton = ({ cardCount = 3 }: RecommendationSkeletonProps) => {
  return (
    <div className="bg-white w-full h-full px-4 py-2 rounded-xl flex flex-col overflow-hidden">
      {/* Header skeleton */}
      <div className="flex flex-row items-center justify-between flex-shrink-0">
        <div>
          {/* Title skeleton */}
          <div className="w-32 h-6 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="flex flex-row items-center gap-2">
          {/* Filter icon skeleton */}
          <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
          {/* Filter text skeleton */}
          <div className="w-10 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Cards container */}
      <div className="flex-1 mt-1 flex flex-col justify-between min-h-0 overflow-hidden">
        <RecommendationCardSkeleton
          count={cardCount}
          showViewAll
        />
      </div>
    </div>
  );
};

export default RecommendationSkeleton;
